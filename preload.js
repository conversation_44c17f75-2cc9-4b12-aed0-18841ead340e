// uTools插件预加载脚本

// 保存当前功能代码和payload
let currentFeatureCode = null;
let currentPayload = null;

// 监听uTools进入事件
if (typeof utools !== 'undefined') {
    try {
        // 通用事件处理函数
        const handlePluginEvent = ({ code, type, payload, from, option }) => {
            currentFeatureCode = code;
            currentPayload = payload;
            window.currentFeatureCode = code;
            window.currentPayload = payload;

            // 触发自定义事件通知主程序
            window.dispatchEvent(new CustomEvent('utools-plugin-enter', {
                detail: { code, type, payload, from, option }
            }));
        };

        // 设置插件进入监听
        utools.onPluginEnter(handlePluginEvent);

        // 监听主窗口推送事件（用于处理图片匹配）
        utools.onMainPush((eventData) => {
            handlePluginEvent(eventData);
            return true; // 返回true表示进入插件应用
        });

        // 监听插件退出
        utools.onPluginOut(() => {
            // 根据配置决定是否清空相应页面内容
            if (window.ocrPlugin && window.ocrPlugin.uiManager) {
                const currentView = window.ocrPlugin.uiManager.currentView;
                const config = window.ocrPlugin.configManager?.getConfig();

                if (currentView === 'translate' && config?.ui?.autoCleanTranslate) {
                    window.ocrPlugin.uiManager.clearTranslateContentSilently();
                } else if (currentView === 'main' && config?.ui?.autoCleanOCR) {
                    window.ocrPlugin.uiManager.clearOCRContentSilently();
                } else if (currentView === 'image-translate' && config?.ui?.autoCleanImageTranslate) {
                    window.ocrPlugin.uiManager.clearImageTranslateContentSilently();
                }
            }

            [currentFeatureCode, currentPayload, window.currentFeatureCode, window.currentPayload] = [null, null, null, null];
        });
    } catch (error) {
        console.error('设置uTools事件监听失败:', error);
    }
} else {
    console.warn('uTools API 不可用，可能在开发环境中运行');
}

// 暴露API给渲染进程
window.ocrAPI = {
    // 屏幕截图
    screenCapture: (callback) => {
        if (typeof utools !== 'undefined' && utools.screenCapture) {
            let screenshotCompleted = false;
            let focusListener = null;

            // 完成截图的通用函数
            const completeScreenshot = (image, reason) => {
                if (screenshotCompleted) return;

                screenshotCompleted = true;

                // 清理监听器
                if (focusListener) {
                    window.removeEventListener('focus', focusListener);
                    focusListener = null;
                }

                if (callback) {
                    callback(image);
                }
            };

            // 监听窗口焦点变化来检测用户取消截图
            focusListener = () => {
                // 延迟一点检查，确保不是正常的焦点切换
                setTimeout(() => {
                    if (!screenshotCompleted) {
                        completeScreenshot(null, '用户取消');
                    }
                }, 100);
            };

            window.addEventListener('focus', focusListener);

            try {
                utools.screenCapture((image) => {
                    if (image) {
                        completeScreenshot(image, 'uTools回调');
                    } else {
                        completeScreenshot(null, 'uTools回调');
                    }
                });
            } catch (error) {
                console.error('截图API调用失败:', error);
                completeScreenshot(null, 'API调用失败');
            }
        } else {
            console.error('🔴 screenCapture API 不可用');
            if (callback) callback(null);
        }
    },

    // 安全调用utools API的通用方法
    safeCall: (apiName, ...args) => {
        if (typeof utools !== 'undefined' && utools[apiName]) {
            try {
                return utools[apiName](...args);
            } catch (error) {
                console.error(`调用${apiName} API失败:`, error);
                return null;
            }
        } else {
            // 在开发环境中，这是正常情况，不需要显示错误
            if (apiName === 'db.get' || apiName === 'db.put' || apiName === 'db.remove') {
                // 在开发环境中完全静默，不显示任何日志
                return null;
            } else {
                console.error(`${apiName} API 不可用`);
            }
            return null;
        }
    },

    // 数据库操作
    db: {
        get: (id) => window.ocrAPI.safeCall('db.get', id) || utools?.db?.get(id),
        put: (doc) => window.ocrAPI.safeCall('db.put', doc) || utools?.db?.put(doc),
        remove: (id) => window.ocrAPI.safeCall('db.remove', id) || utools?.db?.remove(id)
    },

    // 复制到剪贴板
    copyText: (text) => window.ocrAPI.safeCall('copyText', text),

    // 隐藏窗口
    hideMainWindow: () => window.ocrAPI.safeCall('hideMainWindow'),

    // 显示窗口
    showMainWindow: () => window.ocrAPI.safeCall('showMainWindow'),

    // 获取当前功能代码
    getCurrentFeature: () => {
        // 优先从保存的功能代码获取
        if (currentFeatureCode) {
            return currentFeatureCode;
        }
        
        if (window.currentFeatureCode) {
            return window.currentFeatureCode;
        }
        
        // 尝试从uTools API获取
        try {
            // 检查是否有payload，根据payload类型推断功能
            let payload = currentPayload || window.currentPayload;
            
            // 只有在uTools API可用且getPayload方法存在时才调用
            if (!payload && typeof utools !== 'undefined' && typeof utools.getPayload === 'function') {
                payload = utools.getPayload();
            }
            
            if (payload) {
                if (payload.type === 'img') {
                    currentFeatureCode = 'ocr-clipboard';
                    return 'ocr-clipboard';
                } else if (payload.type === 'files') {
                    currentFeatureCode = 'ocr-files';
                    return 'ocr-files';
                } else if (payload.text) {
                    // 文本匹配指令
                    currentFeatureCode = 'text-translate';
                    return 'text-translate';
                }
            }
        } catch (error) {
            console.error('获取payload失败:', error);
        }
        
        // 默认返回null，让主程序处理
        return null;
    },

    // 获取payload数据
    getPayload: () => {
        try {
            let payload = currentPayload || window.currentPayload;

            // 只有在uTools API可用且getPayload方法存在时才调用
            if (!payload && typeof utools !== 'undefined' && typeof utools.getPayload === 'function') {
                payload = utools.getPayload();
            }


            return payload || null;
        } catch (error) {
            console.error('获取payload失败:', error);
            return null;
        }
    },

    // 读取文件（如果uTools支持）
    readFile: (filePath) => {
        try {
            if (typeof utools !== 'undefined' && typeof utools.readFile === 'function') {
                return utools.readFile(filePath);
            } else {
                return null;
            }
        } catch (error) {
            console.error('读取文件失败:', error);
            return null;
        }
    },

    // 检查文件是否存在（如果uTools支持）
    fileExists: (filePath) => {
        try {
            if (typeof utools !== 'undefined' && typeof utools.fileExists === 'function') {
                return utools.fileExists(filePath);
            } else if (typeof utools !== 'undefined' && typeof utools.readFile === 'function') {
                // 尝试读取文件来检查是否存在
                try {
                    const result = utools.readFile(filePath);
                    return result !== null && result !== undefined;
                } catch (e) {
                    return false;
                }
            } else {
                return false;
            }
        } catch (error) {
            console.error('检查文件存在性失败:', error);
            return false;
        }
    },

    // 显示文件选择对话框
    showFileDialog: (callback) => {
        try {
            if (typeof utools !== 'undefined' && typeof utools.showOpenDialog === 'function') {
                const result = utools.showOpenDialog({
                    title: '选择图片文件',
                    filters: [
                        { name: '图片文件', extensions: ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'tiff', 'svg'] }
                    ],
                    properties: ['openFile']
                });
                if (callback) callback(result);
                return result;
            } else {
                if (callback) callback(null);
                return null;
            }
        } catch (error) {
            console.error('显示文件选择对话框失败:', error);
            if (callback) callback(null);
            return null;
        }
    },

    // 获取鼠标位置
    getCursorPosition: () => {
        try {
            if (typeof utools !== 'undefined' && utools.getCursorScreenPoint) {
                return utools.getCursorScreenPoint();
            }
        } catch (error) {
            console.error('[状态指示器] 获取鼠标位置失败:', error);
        }
        // 默认位置
        return { x: 100, y: 100 };
    },

    // 显示系统通知
    showNotification: (message, type = 'info') => {
        try {
            if (typeof utools !== 'undefined' && typeof utools.showNotification === 'function') {
                // 使用uTools的通知API
                utools.showNotification(message);
            } else {
                // 备用方案：使用浏览器通知API
                if ('Notification' in window) {
                    if (Notification.permission === 'granted') {
                        new Notification('OCR Pro', {
                            body: message,
                            icon: 'assets/logo.png'
                        });
                    } else if (Notification.permission !== 'denied') {
                        Notification.requestPermission().then(permission => {
                            if (permission === 'granted') {
                                new Notification('OCR Pro', {
                                    body: message,
                                    icon: 'assets/logo.png'
                                });
                            }
                        });
                    }
                } else {
                    // 最后的备用方案：控制台输出
                    console.log(`[OCR Pro] ${type.toUpperCase()}: ${message}`);
                }
            }
        } catch (error) {
            console.error('显示通知失败:', error);
            // 最后的备用方案：控制台输出
            console.log(`[OCR Pro] ${type.toUpperCase()}: ${message}`);
        }
    },

    // 状态指示器API - 用于静默模式下显示OCR处理状态
    statusIndicator: {
        window: null,           // 当前活动的状态指示器窗口实例
        preCreatedWindow: null, // 预创建的窗口实例
        autoHideTimer: null,    // 自动隐藏定时器
        backupTimer: null,      // 备用清理定时器
        isWindowReady: false,   // 当前窗口是否已准备就绪
        isPreCreatedReady: false, // 预创建窗口是否已准备就绪
        preCreateTimer: null,   // 预创建定时器

        // 预创建状态指示器窗口 - 实现<20ms响应时间
        preCreateWindow: () => {
            try {
                if (window.ocrAPI.statusIndicator.preCreatedWindow) {
                    return; // 已经预创建了
                }

                console.log('[状态指示器] 开始预创建窗口');
                const startTime = performance.now();

                // 创建隐藏的预备窗口
                const statusUrl = `assets/status-indicator.html`;
                const options = {
                    width: 48,
                    height: 48,
                    x: -1000, // 放在屏幕外，避免闪烁
                    y: -1000,
                    frame: false,
                    transparent: true,
                    alwaysOnTop: true,
                    skipTaskbar: true,
                    resizable: false,
                    minimizable: false,
                    maximizable: false,
                    closable: false,
                    focusable: false,
                    show: false, // 预创建时不显示
                    webSecurity: false,
                    enableLargerThanScreen: true,
                    backgroundColor: 'rgba(0, 0, 0, 0)',
                    hasShadow: false,
                    roundedCorners: true
                };

                if (typeof utools !== 'undefined' && typeof utools.createBrowserWindow === 'function') {
                    window.ocrAPI.statusIndicator.preCreatedWindow = utools.createBrowserWindow(statusUrl, options);
                    window.ocrAPI.statusIndicator.isPreCreatedReady = false;

                    // 等待预创建窗口加载完成
                    setTimeout(() => {
                        window.ocrAPI.statusIndicator.isPreCreatedReady = true;
                        const endTime = performance.now();
                        console.log(`[状态指示器] 预创建窗口完成 (${(endTime - startTime).toFixed(2)}ms)`);
                    }, 100); // 预创建可以等待更长时间
                }
            } catch (error) {
                console.error('[状态指示器] 预创建窗口失败:', error);
            }
        },

        // 显示状态指示器 - 超快响应版本（<20ms）
        show: (status = 'loading', autoHide = true, hideDelay = 2000) => {
            try {
                const startTime = performance.now();

                // 优先使用预创建窗口实现超快响应
                if (window.ocrAPI.statusIndicator.preCreatedWindow && window.ocrAPI.statusIndicator.isPreCreatedReady) {
                    console.log('[状态指示器] 使用预创建窗口，实现超快响应');

                    // 获取鼠标位置
                    const mousePos = window.ocrAPI.getCursorPosition();
                    const position = { x: mousePos.x + 8, y: mousePos.y - 8 };

                    // 将预创建窗口移动到正确位置并显示
                    const preWindow = window.ocrAPI.statusIndicator.preCreatedWindow;
                    preWindow.setPosition(position.x, position.y);
                    preWindow.show();

                    // 切换到活动窗口
                    window.ocrAPI.statusIndicator.window = preWindow;
                    window.ocrAPI.statusIndicator.isWindowReady = true;
                    window.ocrAPI.statusIndicator.preCreatedWindow = null;
                    window.ocrAPI.statusIndicator.isPreCreatedReady = false;

                    // 立即发送状态更新
                    window.ocrAPI.statusIndicator.sendStatusUpdate(status, autoHide, hideDelay);

                    // 异步预创建下一个窗口
                    setTimeout(() => {
                        window.ocrAPI.statusIndicator.preCreateWindow();
                    }, 100);

                    const endTime = performance.now();
                    console.log(`[状态指示器] 超快显示完成 (${(endTime - startTime).toFixed(2)}ms)`);
                    return;
                }

                // 如果当前窗口已存在且准备就绪，重用它
                if (window.ocrAPI.statusIndicator.window && window.ocrAPI.statusIndicator.isWindowReady) {
                    if (!window.ocrAPI.statusIndicator.window.isVisible()) {
                        window.ocrAPI.statusIndicator.showWindow();
                    }
                    window.ocrAPI.statusIndicator.sendStatusUpdate(status, autoHide, hideDelay);
                    return;
                }

                // 回退方案：创建新窗口（但仍然优化）
                console.log('[状态指示器] 预创建窗口不可用，使用快速创建');
                window.ocrAPI.statusIndicator.forceDestroy();

                const mousePos = window.ocrAPI.getCursorPosition();
                const position = { x: mousePos.x + 8, y: mousePos.y - 8 };

                const statusUrl = `assets/status-indicator.html`;
                const options = {
                    width: 48,
                    height: 48,
                    x: position.x,
                    y: position.y,
                    frame: false,
                    transparent: true,
                    alwaysOnTop: true,
                    skipTaskbar: true,
                    resizable: false,
                    minimizable: false,
                    maximizable: false,
                    closable: false,
                    focusable: false,
                    show: true,
                    webSecurity: false,
                    enableLargerThanScreen: true,
                    backgroundColor: 'rgba(0, 0, 0, 0)',
                    hasShadow: false,
                    roundedCorners: true
                };

                if (typeof utools !== 'undefined' && typeof utools.createBrowserWindow === 'function') {
                    window.ocrAPI.statusIndicator.window = utools.createBrowserWindow(statusUrl, options);
                    window.ocrAPI.statusIndicator.isWindowReady = false;

                    // 进一步减少等待时间
                    setTimeout(() => {
                        window.ocrAPI.statusIndicator.isWindowReady = true;
                        window.ocrAPI.statusIndicator.sendStatusUpdate(status, autoHide, hideDelay);

                        // 创建完成后立即预创建下一个
                        setTimeout(() => {
                            window.ocrAPI.statusIndicator.preCreateWindow();
                        }, 200);
                    }, 30); // 进一步减少到30ms

                } else {
                    console.warn('[状态指示器] createBrowserWindow API 不可用');
                }

                const endTime = performance.now();
                console.log(`[状态指示器] 快速创建完成 (${(endTime - startTime).toFixed(2)}ms)`);
            } catch (error) {
                console.error('[状态指示器] 显示失败:', error);
            }
        },

        // 发送状态更新消息到窗口
        sendStatusUpdate: (status, autoHide = true, hideDelay = 2000) => {
            try {
                const indicatorWindow = window.ocrAPI.statusIndicator.window;
                if (indicatorWindow && window.ocrAPI.statusIndicator.isWindowReady) {
                    // 通过postMessage发送状态更新
                    indicatorWindow.webContents.executeJavaScript(`
                        if (window.statusIndicator) {
                            window.statusIndicator.updateStatus('${status}', ${autoHide}, ${hideDelay});
                        }
                    `).catch(error => {
                        console.warn('[状态指示器] 发送状态更新失败:', error);
                        // 如果发送失败，可能窗口已损坏，重新创建
                        window.ocrAPI.statusIndicator.isWindowReady = false;
                        window.ocrAPI.statusIndicator.show(status, autoHide, hideDelay);
                    });

                    // 设置自动隐藏定时器
                    if (autoHide && status !== 'loading') {
                        window.ocrAPI.statusIndicator.setAutoHideTimer(hideDelay);
                    }

                    console.log(`[状态指示器] 状态更新发送: ${status}`);
                }
            } catch (error) {
                console.error('[状态指示器] 发送状态更新失败:', error);
            }
        },

        // 更新状态 - 使用消息传递而非重建窗口
        updateStatus: (status, autoHide = true, hideDelay = 2000) => {
            try {
                // 如果窗口存在且准备就绪，直接发送更新消息
                if (window.ocrAPI.statusIndicator.window && window.ocrAPI.statusIndicator.isWindowReady) {
                    window.ocrAPI.statusIndicator.sendStatusUpdate(status, autoHide, hideDelay);

                    // 设置备用定时器确保窗口能被清理
                    if (autoHide && status !== 'loading') {
                        window.ocrAPI.statusIndicator.clearBackupTimer();
                        window.ocrAPI.statusIndicator.backupTimer = setTimeout(() => {
                            // 备用清理：如果窗口仍然存在且不可见，则销毁
                            if (window.ocrAPI.statusIndicator.window &&
                                !window.ocrAPI.statusIndicator.window.isVisible()) {
                                window.ocrAPI.statusIndicator.forceDestroy();
                            }
                        }, hideDelay + 2000); // 给更多时间
                    }
                } else {
                    // 窗口不存在或未准备就绪，创建新窗口
                    window.ocrAPI.statusIndicator.show(status, autoHide, hideDelay);
                }
            } catch (error) {
                console.error('[状态指示器] 更新状态失败:', error);
            }
        },

        // 隐藏状态指示器
        hide: () => {
            try {
                const indicatorWindow = window.ocrAPI.statusIndicator.window;
                if (indicatorWindow) {
                    indicatorWindow.hide();
                }
            } catch (error) {
                console.error('[状态指示器] 隐藏失败:', error);
            }
        },

        // 显示已隐藏的状态指示器
        showWindow: () => {
            try {
                const indicatorWindow = window.ocrAPI.statusIndicator.window;
                if (indicatorWindow && window.ocrAPI.statusIndicator.isWindowReady) {
                    indicatorWindow.show();
                    return true;
                }
                return false;
            } catch (error) {
                console.error('[状态指示器] 显示窗口失败:', error);
                return false;
            }
        },

        // 销毁状态指示器窗口
        destroy: () => {
            try {
                window.ocrAPI.statusIndicator.clearAutoHideTimer();
                window.ocrAPI.statusIndicator.clearBackupTimer();

                const indicatorWindow = window.ocrAPI.statusIndicator.window;
                if (indicatorWindow) {
                    try {
                        indicatorWindow.close();
                    } catch (e) {
                        console.warn('[状态指示器] 关闭窗口失败:', e);
                    }
                    window.ocrAPI.statusIndicator.window = null;
                }
            } catch (error) {
                console.error('[状态指示器] 销毁窗口失败:', error);
                window.ocrAPI.statusIndicator.window = null;
            }
        },

        // 强制销毁窗口
        forceDestroy: () => {
            try {
                // 清除所有定时器
                window.ocrAPI.statusIndicator.clearAutoHideTimer();
                window.ocrAPI.statusIndicator.clearBackupTimer();

                if (window.ocrAPI.statusIndicator.preCreateTimer) {
                    clearTimeout(window.ocrAPI.statusIndicator.preCreateTimer);
                    window.ocrAPI.statusIndicator.preCreateTimer = null;
                }

                // 销毁当前窗口
                const indicatorWindow = window.ocrAPI.statusIndicator.window;
                if (indicatorWindow) {
                    try {
                        indicatorWindow.hide();
                        indicatorWindow.close();
                    } catch (e) {
                        console.warn('[状态指示器] 关闭当前窗口失败:', e);
                    }
                    window.ocrAPI.statusIndicator.window = null;
                }

                // 销毁预创建窗口
                const preCreatedWindow = window.ocrAPI.statusIndicator.preCreatedWindow;
                if (preCreatedWindow) {
                    try {
                        preCreatedWindow.hide();
                        preCreatedWindow.close();
                    } catch (e) {
                        console.warn('[状态指示器] 关闭预创建窗口失败:', e);
                    }
                    window.ocrAPI.statusIndicator.preCreatedWindow = null;
                }

                // 重置所有状态
                window.ocrAPI.statusIndicator.isWindowReady = false;
                window.ocrAPI.statusIndicator.isPreCreatedReady = false;
            } catch (error) {
                console.error('[状态指示器] 强制销毁失败:', error);
                window.ocrAPI.statusIndicator.window = null;
                window.ocrAPI.statusIndicator.preCreatedWindow = null;
                window.ocrAPI.statusIndicator.isWindowReady = false;
                window.ocrAPI.statusIndicator.isPreCreatedReady = false;
            }
        },

        // 初始化状态指示器系统
        init: () => {
            try {
                console.log('[状态指示器] 初始化系统');
                // 延迟预创建，避免影响插件启动速度
                window.ocrAPI.statusIndicator.preCreateTimer = setTimeout(() => {
                    window.ocrAPI.statusIndicator.preCreateWindow();
                }, 1000); // 插件启动1秒后预创建
            } catch (error) {
                console.error('[状态指示器] 初始化失败:', error);
            }
        },

        // 设置自动隐藏定时器
        setAutoHideTimer: (delay) => {
            window.ocrAPI.statusIndicator.clearAutoHideTimer();
            window.ocrAPI.statusIndicator.autoHideTimer = setTimeout(() => {
                // 先隐藏窗口，而不是直接销毁
                window.ocrAPI.statusIndicator.hide();

                // 延迟销毁窗口，给后续可能的状态更新留出时间
                setTimeout(() => {
                    // 只有在窗口确实没有被重新使用时才销毁
                    if (window.ocrAPI.statusIndicator.window &&
                        !window.ocrAPI.statusIndicator.window.isVisible()) {
                        window.ocrAPI.statusIndicator.forceDestroy();
                    }
                }, 1000); // 1秒后再检查是否需要销毁
            }, delay);
        },

        // 清除自动隐藏定时器
        clearAutoHideTimer: () => {
            if (window.ocrAPI.statusIndicator.autoHideTimer) {
                clearTimeout(window.ocrAPI.statusIndicator.autoHideTimer);
                window.ocrAPI.statusIndicator.autoHideTimer = null;
            }
        },

        // 清除备用定时器
        clearBackupTimer: () => {
            if (window.ocrAPI.statusIndicator.backupTimer) {
                clearTimeout(window.ocrAPI.statusIndicator.backupTimer);
                window.ocrAPI.statusIndicator.backupTimer = null;
            }
        }
    }
};

// 插件加载完成后初始化状态指示器系统
if (typeof window !== 'undefined') {
    window.addEventListener('load', () => {
        // 延迟初始化，确保所有API都已准备就绪
        setTimeout(() => {
            if (window.ocrAPI?.statusIndicator?.init) {
                window.ocrAPI.statusIndicator.init();
            }
        }, 500);
    });
}
