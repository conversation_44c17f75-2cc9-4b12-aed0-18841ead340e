/**
 * 统一状态管理器测试脚本
 * 用于验证统一状态管理架构的功能完整性和向后兼容性
 */
class UnifiedStatusTest {
    constructor() {
        this.testResults = [];
        this.passedTests = 0;
        this.failedTests = 0;
    }
    
    // 运行所有测试
    async runAllTests() {
        console.log('🧪 开始运行统一状态管理器测试...');
        
        // 基础功能测试
        this.testBasicFunctionality();
        
        // 传统OCR服务测试
        this.testTraditionalOCRServices();
        
        // AI模型服务测试
        this.testAIModelServices();
        
        // 配置变更检测测试
        this.testConfigChangeDetection();
        
        // 缓存清除测试
        this.testCacheClearance();
        
        // 向后兼容性测试
        this.testBackwardCompatibility();
        
        // 输出测试结果
        this.outputTestResults();
    }
    
    // 测试基础功能
    testBasicFunctionality() {
        this.addTest('统一服务状态管理器初始化', () => {
            return window.unifiedServiceStatusManager !== undefined;
        });
        
        this.addTest('管理器引用设置', () => {
            return window.unifiedServiceStatusManager.modelStatusManager !== null &&
                   window.unifiedServiceStatusManager.configManager !== null;
        });
        
        this.addTest('服务类型识别', () => {
            const manager = window.unifiedServiceStatusManager;
            return manager.isTraditionalOCRService('baidu') &&
                   manager.isAIModelService('openai') &&
                   !manager.isTraditionalOCRService('openai') &&
                   !manager.isAIModelService('baidu');
        });
    }
    
    // 测试传统OCR服务
    testTraditionalOCRServices() {
        this.addTest('传统OCR服务状态获取', () => {
            const manager = window.unifiedServiceStatusManager;
            const status = manager.getServiceStatus('baidu');
            return status && typeof status.status === 'string' && typeof status.message === 'string';
        });
        
        this.addTest('传统OCR服务可用性检查', () => {
            const manager = window.unifiedServiceStatusManager;
            const isAvailable = manager.isServiceAvailable('baidu');
            return typeof isAvailable === 'boolean';
        });
    }
    
    // 测试AI模型服务
    testAIModelServices() {
        this.addTest('AI模型服务状态获取', () => {
            const manager = window.unifiedServiceStatusManager;
            const status = manager.getServiceStatus('openai');
            return status && typeof status.status === 'string' && typeof status.message === 'string';
        });
        
        this.addTest('AI模型服务可用性检查', () => {
            const manager = window.unifiedServiceStatusManager;
            const isAvailable = manager.isServiceAvailable('openai');
            return typeof isAvailable === 'boolean';
        });
        
        this.addTest('特定AI模型状态获取', () => {
            const manager = window.unifiedServiceStatusManager;
            const status = manager.getServiceStatus('openai', 'gpt-4o');
            return status && typeof status.status === 'string';
        });
    }
    
    // 测试配置变更检测
    testConfigChangeDetection() {
        this.addTest('配置哈希生成', () => {
            const manager = window.unifiedServiceStatusManager;
            const hash1 = manager.generateServiceConfigHash('baidu', { baidu: { apiKey: 'test1' } });
            const hash2 = manager.generateServiceConfigHash('baidu', { baidu: { apiKey: 'test2' } });
            return hash1 !== hash2 && typeof hash1 === 'string' && typeof hash2 === 'string';
        });
        
        this.addTest('配置变更检测', () => {
            const manager = window.unifiedServiceStatusManager;
            const oldConfig = { baidu: { apiKey: 'old' } };
            const newConfig = { baidu: { apiKey: 'new' } };
            const hasChanged = manager.hasConfigChanged('baidu', oldConfig, newConfig);
            return hasChanged === true;
        });
    }
    
    // 测试缓存清除
    testCacheClearance() {
        this.addTest('单个服务缓存清除', () => {
            const manager = window.unifiedServiceStatusManager;
            const result = manager.clearServiceCache('baidu');
            return typeof result === 'boolean';
        });
        
        this.addTest('所有服务缓存清除', () => {
            const manager = window.unifiedServiceStatusManager;
            const result = manager.clearAllServiceCache();
            return typeof result === 'boolean';
        });
    }
    
    // 测试向后兼容性
    testBackwardCompatibility() {
        this.addTest('主应用状态管理方法存在', () => {
            return window.ocrApp && 
                   typeof window.ocrApp.determineMainPageServiceStatus === 'function' &&
                   typeof window.ocrApp.isServiceAvailable === 'function' &&
                   typeof window.ocrApp.clearServiceStatusCache === 'function';
        });
        
        this.addTest('传统缓存方法兼容性', () => {
            return window.ocrApp &&
                   typeof window.ocrApp.getCachedServiceStatus === 'function' &&
                   typeof window.ocrApp.setCachedServiceStatus === 'function';
        });
        
        this.addTest('配置哈希方法兼容性', () => {
            return window.ocrApp &&
                   typeof window.ocrApp.generateServiceConfigHash === 'function' &&
                   typeof window.ocrApp.detectChangedServices === 'function';
        });
    }
    
    // 添加测试用例
    addTest(name, testFunction) {
        try {
            const result = testFunction();
            if (result) {
                this.testResults.push({ name, status: 'PASS', error: null });
                this.passedTests++;
            } else {
                this.testResults.push({ name, status: 'FAIL', error: 'Test returned false' });
                this.failedTests++;
            }
        } catch (error) {
            this.testResults.push({ name, status: 'ERROR', error: error.message });
            this.failedTests++;
        }
    }
    
    // 输出测试结果
    outputTestResults() {
        console.log('\n📊 统一状态管理器测试结果:');
        console.log(`✅ 通过: ${this.passedTests}`);
        console.log(`❌ 失败: ${this.failedTests}`);
        console.log(`📈 成功率: ${((this.passedTests / (this.passedTests + this.failedTests)) * 100).toFixed(1)}%`);
        
        console.log('\n📋 详细结果:');
        this.testResults.forEach(result => {
            const icon = result.status === 'PASS' ? '✅' : '❌';
            console.log(`${icon} ${result.name}: ${result.status}`);
            if (result.error) {
                console.log(`   错误: ${result.error}`);
            }
        });
        
        // 返回测试是否全部通过
        return this.failedTests === 0;
    }
    
    // 获取测试统计
    getTestStats() {
        return {
            total: this.passedTests + this.failedTests,
            passed: this.passedTests,
            failed: this.failedTests,
            successRate: (this.passedTests / (this.passedTests + this.failedTests)) * 100
        };
    }
}

// 导出测试类
if (typeof window !== 'undefined') {
    window.UnifiedStatusTest = UnifiedStatusTest;
    
    // 自动运行测试（延迟执行，确保所有组件加载完成）
    setTimeout(() => {
        if (window.unifiedServiceStatusManager && window.ocrApp) {
            const test = new UnifiedStatusTest();
            test.runAllTests();
        }
    }, 2000);
}
