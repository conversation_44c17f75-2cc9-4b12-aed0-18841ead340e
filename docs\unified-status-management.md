# 统一AI模型服务状态管理架构

## 概述

本项目实现了统一的AI模型服务状态管理架构，将传统OCR服务（百度、腾讯、阿里云）和AI模型服务（OpenAI、Anthropic、Google等）的状态管理统一到一个接口下，提供一致的API调用方式。

## 架构特点

### 1. 统一存储策略
- **传统OCR服务**：从24小时TTL改为永久存储策略
- **AI模型服务**：保持永久存储策略
- **一致性**：两类服务使用相同的持久化机制

### 2. 统一缓存失效策略
- 使用相同的配置哈希算法检测配置变更
- 配置变更时自动触发状态重新检测
- 支持手动清除特定服务或所有服务的缓存

### 3. 向后兼容性
- 保持现有API接口不变
- 新的统一接口作为增强功能
- 原有功能继续正常工作

## 核心组件

### UnifiedServiceStatusManager
统一服务状态管理器，提供以下功能：

```javascript
// 获取服务状态
const status = window.unifiedServiceStatusManager.getServiceStatus('baidu');
const aiStatus = window.unifiedServiceStatusManager.getServiceStatus('openai', 'gpt-4o');

// 检查服务可用性
const isAvailable = window.unifiedServiceStatusManager.isServiceAvailable('baidu');

// 检测配置变更
const hasChanged = window.unifiedServiceStatusManager.hasConfigChanged('baidu', oldConfig, newConfig);

// 清除服务缓存
window.unifiedServiceStatusManager.clearServiceCache('baidu');
window.unifiedServiceStatusManager.clearAllServiceCache();
```

### UnifiedStorageManager
统一存储管理器的更新：

```javascript
// 传统OCR服务状态缓存配置
'ocr_service_status_cache': {
    type: 'dbStorage',
    // 移除TTL限制，改为永久存储策略
    description: '服务状态缓存'
}
```

## 状态数据结构

### 传统OCR服务状态
```javascript
{
    status: 'ready' | 'error' | 'unconfigured' | 'unknown',
    message: '状态描述',
    modelName: '服务名称',
    timestamp: 时间戳,
    configHash: '全局配置哈希',
    serviceConfigHash: '服务特定配置哈希'
}
```

### AI模型服务状态
```javascript
{
    status: 'success' | 'failed' | 'testing' | 'unknown',
    message: '状态描述',
    modelName: '模型ID',
    timestamp: 时间戳,
    error: '错误信息（可选）',
    lastUpdated: '最后更新时间',
    testCount: '测试次数'
}
```

## 配置哈希算法

### 传统OCR服务配置
```javascript
{
    apiKey: '',
    secretKey: '',
    secretId: '',
    accessKey: '',
    accessSecret: '',
    region: ''
}
```

### AI模型服务配置
```javascript
{
    apiKey: '',
    baseUrl: '',
    model: '',
    useCustomModel: false,
    customModel: '',
    maxTokens: 0
}
```

## 使用示例

### 1. 检查服务状态
```javascript
// 统一接口
const status = window.unifiedServiceStatusManager.getServiceStatus('baidu');
console.log(`服务状态: ${status.status}, 消息: ${status.message}`);

// 原有接口（仍然可用）
const legacyStatus = window.ocrApp.determineMainPageServiceStatus('baidu');
```

### 2. 配置变更处理
```javascript
// 检测配置变更
const hasChanged = window.unifiedServiceStatusManager.hasConfigChanged(
    'baidu', 
    oldConfig, 
    newConfig
);

if (hasChanged) {
    // 清除缓存并重新检测
    window.unifiedServiceStatusManager.clearServiceCache('baidu');
}
```

### 3. 批量状态更新
```javascript
// 更新所有服务状态指示器
window.ocrApp.updateAllServiceIndicators();

// 清除所有缓存
window.unifiedServiceStatusManager.clearAllServiceCache();
```

## 迁移指南

### 现有代码无需修改
所有现有的状态管理调用都会继续工作：

```javascript
// 这些调用仍然有效
window.ocrApp.isServiceAvailable('baidu');
window.ocrApp.getCachedServiceStatus('baidu');
window.ocrApp.clearServiceStatusCache('baidu');
```

### 推荐的新用法
对于新功能，建议使用统一接口：

```javascript
// 推荐使用统一接口
window.unifiedServiceStatusManager.getServiceStatus('baidu');
window.unifiedServiceStatusManager.isServiceAvailable('baidu');
window.unifiedServiceStatusManager.clearServiceCache('baidu');
```

## 测试验证

项目包含完整的测试套件来验证统一状态管理架构：

```javascript
// 运行测试
const test = new UnifiedStatusTest();
test.runAllTests();

// 获取测试结果
const stats = test.getTestStats();
console.log(`测试成功率: ${stats.successRate}%`);
```

## 性能优化

### 1. 永久存储策略
- 减少不必要的状态重新检测
- 提高应用启动速度
- 保持状态一致性

### 2. 统一缓存管理
- 避免重复的配置哈希计算
- 统一的缓存失效策略
- 更好的内存使用效率

### 3. 向后兼容性
- 渐进式迁移，无破坏性变更
- 保持现有功能稳定性
- 新功能作为增强而非替换

## 故障排除

### 常见问题

1. **统一管理器未初始化**
   - 检查HTML中是否正确引入了`unified-service-status-manager.js`
   - 确保在main.js加载后初始化

2. **状态不一致**
   - 清除所有缓存：`window.unifiedServiceStatusManager.clearAllServiceCache()`
   - 重新启动应用

3. **配置变更未生效**
   - 检查配置哈希是否正确生成
   - 手动清除相关服务缓存

### 调试工具

```javascript
// 检查统一管理器状态
console.log(window.unifiedServiceStatusManager);

// 检查模型状态管理器
console.log(window.modelStatusManager.getCacheStats());

// 检查存储统计
console.log(window.unifiedStorage.getStorageStats());
```
