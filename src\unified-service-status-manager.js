/**
 * 统一服务状态管理器
 * 封装传统OCR服务和AI模型服务的状态管理差异，提供一致的API接口
 */
class UnifiedServiceStatusManager {
    constructor() {
        // 传统OCR服务列表
        this.traditionalOCRServices = ['baidu', 'tencent', 'aliyun'];
        
        // AI模型服务列表
        this.aiModelServices = ['openai', 'anthropic', 'google', 'alibaba', 'bytedance', 'ocrpro', 'utools'];
        
        // 所有支持的服务
        this.allServices = [...this.traditionalOCRServices, ...this.aiModelServices];
        
        // 引用外部管理器（将在初始化时设置）
        this.modelStatusManager = null;
        this.legacyServiceStatusCache = null;
        this.configManager = null;
        
        this.init();
    }
    
    // 初始化
    init() {
        // 延迟初始化，等待其他管理器加载完成
        setTimeout(() => {
            this.setupManagerReferences();
        }, 100);
    }
    
    // 设置管理器引用
    setupManagerReferences() {
        // 获取模型状态管理器引用
        if (window.modelStatusManager) {
            this.modelStatusManager = window.modelStatusManager;
        }
        
        // 获取配置管理器引用
        if (window.configManager) {
            this.configManager = window.configManager;
        }
    }
    
    // 设置传统服务状态缓存引用（由main.js调用）
    setLegacyServiceStatusCache(cache) {
        this.legacyServiceStatusCache = cache;
    }
    
    // 设置外部管理器引用（由main.js调用）
    setManagerReferences(modelStatusManager, configManager) {
        this.modelStatusManager = modelStatusManager;
        this.configManager = configManager;
    }
    
    /**
     * 统一的服务状态获取接口
     * @param {string} serviceName - 服务名称
     * @param {string} modelId - 模型ID（AI服务需要）
     * @returns {Object} 统一的状态对象
     */
    getServiceStatus(serviceName, modelId = null) {
        if (!serviceName) {
            return this.createStatusResult('unconfigured', '请先配置模型', null);
        }
        
        if (this.isTraditionalOCRService(serviceName)) {
            return this.getTraditionalServiceStatus(serviceName);
        } else if (this.isAIModelService(serviceName)) {
            return this.getAIServiceStatus(serviceName, modelId);
        } else {
            return this.createStatusResult('unknown', '不支持的服务类型', null);
        }
    }
    
    /**
     * 统一的服务状态更新接口
     * @param {string} serviceName - 服务名称
     * @param {string} status - 状态类型
     * @param {string} message - 状态消息
     * @param {string} modelId - 模型ID（AI服务需要）
     * @param {string} error - 错误信息（可选）
     * @returns {boolean} 是否更新成功
     */
    updateServiceStatus(serviceName, status, message, modelId = null, error = null) {
        if (this.isTraditionalOCRService(serviceName)) {
            return this.updateTraditionalServiceStatus(serviceName, status, message);
        } else if (this.isAIModelService(serviceName)) {
            return this.updateAIModelStatus(serviceName, modelId, status, error);
        }
        return false;
    }
    
    /**
     * 统一的服务可用性检查接口
     * @param {string} serviceName - 服务名称
     * @param {string} modelId - 模型ID（AI服务需要）
     * @returns {boolean} 是否可用
     */
    isServiceAvailable(serviceName, modelId = null) {
        const status = this.getServiceStatus(serviceName, modelId);
        return status.status === 'ready' || status.status === 'success';
    }
    
    /**
     * 统一的配置变更检测接口
     * @param {string} serviceName - 服务名称
     * @param {Object} oldConfig - 旧配置
     * @param {Object} newConfig - 新配置
     * @returns {boolean} 配置是否发生变更
     */
    hasConfigChanged(serviceName, oldConfig, newConfig) {
        if (!this.configManager) {
            console.warn('配置管理器未初始化');
            return true; // 保守策略，假设配置已变更
        }
        
        // 使用统一的配置哈希算法
        const oldHash = this.generateServiceConfigHash(serviceName, oldConfig);
        const newHash = this.generateServiceConfigHash(serviceName, newConfig);
        
        return oldHash !== newHash;
    }
    
    /**
     * 统一的缓存清除接口
     * @param {string} serviceName - 服务名称
     * @param {string} modelId - 模型ID（AI服务需要）
     * @returns {boolean} 是否清除成功
     */
    clearServiceCache(serviceName, modelId = null) {
        if (this.isTraditionalOCRService(serviceName)) {
            return this.clearTraditionalServiceCache(serviceName);
        } else if (this.isAIModelService(serviceName)) {
            return this.clearAIModelCache(serviceName, modelId);
        }
        return false;
    }
    
    // ==================== 内部方法 ====================
    
    // 判断是否为传统OCR服务
    isTraditionalOCRService(serviceName) {
        return this.traditionalOCRServices.includes(serviceName);
    }
    
    // 判断是否为AI模型服务
    isAIModelService(serviceName) {
        return this.aiModelServices.includes(serviceName);
    }
    
    // 创建统一的状态结果对象
    createStatusResult(status, message, modelName, additionalInfo = {}) {
        return {
            status,
            message,
            modelName,
            timestamp: Date.now(),
            ...additionalInfo
        };
    }
    
    // 获取传统OCR服务状态
    getTraditionalServiceStatus(serviceName) {
        if (!this.legacyServiceStatusCache) {
            return this.createStatusResult('unknown', '状态管理器未初始化', serviceName);
        }
        
        const cachedStatus = this.legacyServiceStatusCache[serviceName];
        if (cachedStatus && cachedStatus.status) {
            return this.createStatusResult(
                cachedStatus.status.type,
                cachedStatus.status.message,
                serviceName,
                {
                    timestamp: cachedStatus.timestamp,
                    configHash: cachedStatus.configHash,
                    serviceConfigHash: cachedStatus.serviceConfigHash
                }
            );
        }
        
        return this.createStatusResult('unknown', '未测试', serviceName);
    }
    
    // 获取AI服务状态
    getAIServiceStatus(serviceName, modelId = null) {
        if (!this.modelStatusManager) {
            return this.createStatusResult('unknown', '模型状态管理器未初始化', modelId);
        }
        
        if (modelId) {
            // 获取特定模型的状态
            const modelStatus = this.modelStatusManager.getModelStatus(serviceName, modelId);
            return this.createStatusResult(
                modelStatus.status === 'success' ? 'ready' : modelStatus.status,
                this.getStatusMessage(modelStatus.status, modelStatus.error),
                modelId,
                {
                    error: modelStatus.error,
                    lastUpdated: modelStatus.lastUpdated,
                    testCount: modelStatus.testCount
                }
            );
        } else {
            // 获取平台整体状态
            const platformSummary = this.modelStatusManager.getPlatformSummaryStatus(serviceName);
            if (platformSummary.availableCount > 0) {
                return this.createStatusResult('ready', `${platformSummary.availableCount}个可用模型`, null, {
                    availableCount: platformSummary.availableCount,
                    totalCount: platformSummary.totalCount
                });
            } else if (platformSummary.totalCount > 0) {
                return this.createStatusResult('error', '模型未通过测试', null, {
                    failedCount: platformSummary.failedCount,
                    totalCount: platformSummary.totalCount
                });
            } else {
                return this.createStatusResult('unconfigured', '未配置模型', null);
            }
        }
    }
    
    // 更新传统OCR服务状态
    updateTraditionalServiceStatus(serviceName, status, message) {
        if (!this.legacyServiceStatusCache) {
            return false;
        }
        
        // 这里需要调用main.js中的setCachedServiceStatus方法
        // 由于架构限制，我们通过事件或回调的方式来实现
        if (window.ocrApp && window.ocrApp.setCachedServiceStatus) {
            const statusObj = { type: status, message: message };
            window.ocrApp.setCachedServiceStatus(serviceName, statusObj);
            return true;
        }
        
        return false;
    }
    
    // 更新AI模型状态
    updateAIModelStatus(serviceName, modelId, status, error = null) {
        if (!this.modelStatusManager || !modelId) {
            return false;
        }
        
        this.modelStatusManager.updateModelStatus(serviceName, modelId, status, error);
        return true;
    }
    
    // 清除传统OCR服务缓存
    clearTraditionalServiceCache(serviceName) {
        if (window.ocrApp && window.ocrApp.clearServiceStatusCache) {
            window.ocrApp.clearServiceStatusCache(serviceName);
            return true;
        }
        return false;
    }
    
    // 清除AI模型缓存
    clearAIModelCache(serviceName, modelId = null) {
        if (!this.modelStatusManager) {
            return false;
        }

        if (modelId) {
            // 清除特定模型的状态
            this.modelStatusManager.updateModelStatus(serviceName, modelId, 'unknown');
        } else {
            // 清除平台下所有模型的状态
            this.modelStatusManager.clearPlatformStatus(serviceName);
        }
        return true;
    }

    /**
     * 清除所有服务状态缓存
     * @returns {boolean} 是否清除成功
     */
    clearAllServiceCache() {
        let success = true;

        // 清除所有传统OCR服务缓存
        this.traditionalOCRServices.forEach(serviceName => {
            if (!this.clearTraditionalServiceCache(serviceName)) {
                success = false;
            }
        });

        // 清除所有AI模型服务缓存
        this.aiModelServices.forEach(serviceName => {
            if (!this.clearAIModelCache(serviceName)) {
                success = false;
            }
        });

        return success;
    }
    
    // 生成服务配置哈希值（统一算法）
    generateServiceConfigHash(serviceName, config) {
        if (window.ocrApp && window.ocrApp.generateServiceConfigHash) {
            return window.ocrApp.generateServiceConfigHash(serviceName, config);
        }
        
        // 后备哈希算法
        const serviceConfig = this.getStandardServiceConfig(serviceName, config);
        return this.generateHash(serviceConfig);
    }
    
    // 获取标准化服务配置（与main.js保持一致）
    getStandardServiceConfig(serviceName, config) {
        if (!config || !config[serviceName]) {
            return {};
        }
        
        const serviceConfig = config[serviceName];
        
        if (this.isTraditionalOCRService(serviceName)) {
            // 传统OCR服务配置
            return {
                apiKey: serviceConfig.apiKey || '',
                secretKey: serviceConfig.secretKey || '',
                secretId: serviceConfig.secretId || '',
                accessKey: serviceConfig.accessKey || '',
                accessSecret: serviceConfig.accessSecret || '',
                region: serviceConfig.region || ''
            };
        } else {
            // AI模型服务配置
            return {
                apiKey: serviceConfig.apiKey || '',
                baseUrl: serviceConfig.baseUrl || '',
                model: serviceConfig.model || '',
                useCustomModel: serviceConfig.useCustomModel || false,
                customModel: serviceConfig.customModel || '',
                maxTokens: serviceConfig.maxTokens || 0
            };
        }
    }
    
    // 简单哈希算法
    generateHash(obj) {
        const configStr = JSON.stringify(obj, Object.keys(obj).sort());
        let hash = 0;
        for (let i = 0; i < configStr.length; i++) {
            const char = configStr.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return hash.toString();
    }
    
    // 获取状态消息
    getStatusMessage(status, error = null) {
        switch (status) {
            case 'success':
                return '连接正常';
            case 'failed':
                return error || '连接失败';
            case 'testing':
                return '测试中...';
            case 'unknown':
                return '未测试';
            default:
                return '状态未知';
        }
    }
}

// 创建全局实例
if (typeof window !== 'undefined') {
    window.unifiedServiceStatusManager = new UnifiedServiceStatusManager();
    window.UnifiedServiceStatusManager = UnifiedServiceStatusManager;
}
